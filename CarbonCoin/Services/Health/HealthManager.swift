//
//  HealthManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation
import HealthKit

// MARK: - HealthManager 实现
@MainActor
class HealthManager: ObservableObject, HealthManagerProtocol {
    
    // MARK: - Properties
    private let healthStore = HKHealthStore()
    
    /// HealthKit 是否可用
    var isHealthKitAvailable: Bool {
        return HKHealthStore.isHealthDataAvailable()
    }
    
    // MARK: - Authorization
    
    /// 请求 HealthKit 授权
    func requestAuthorization(for types: [HealthDataType]) async throws {
        guard isHealthKitAvailable else { 
            return 
        }
        
        // 过滤掉 quantityType 为 nil 的类型
        let validTypes = types.filter { $0.quantityType != nil }
        guard !validTypes.isEmpty else {
            return
        }
        
        let readTypes = Set(validTypes.compactMap { $0.quantityType })
        try await healthStore.requestAuthorization(toShare: [], read: readTypes)
    }
    
    // MARK: - Data Fetching
    
    /// 获取指定类型和时间段的健康数据
    func fetchHealthData(
        type: HealthDataType,
        period: TimePeriod
    ) async throws -> [HealthData] {
        
        guard isHealthKitAvailable,
              let quantityType = type.quantityType else {
            print("HealthKit not available or quantity type is nil for \(type)")
            return generateFallbackData(type: type, period: period)
        }
        
        let startDate = period.currentPeriodStartDate
        let endDate = Date()
        
        print("Fetching \(type) data from \(startDate) to \(endDate)")
        
        return try await withCheckedThrowingContinuation { continuation in
            let interval = createDateComponents(for: period.aggregationInterval)
            let anchorDate = Calendar.current.startOfDay(for: startDate)
            
            let query = HKStatisticsCollectionQuery(
                quantityType: quantityType,
                quantitySamplePredicate: nil,
                options: getStatisticsOptions(for: type),
                anchorDate: anchorDate,
                intervalComponents: interval
            )
            
            query.initialResultsHandler = { _, results, error in
                if let error = error {
                    // 发生错误时返回降级数据
                    print("Error fetching health data: \(error)")
                    continuation.resume(returning: [])
                    return
                }

                guard let results = results else {
                    print("No results returned from HealthKit")
                    continuation.resume(returning: [])
                    return
                }

                var healthDataArray: [HealthData] = []
                
                results.enumerateStatistics(from: startDate, to: endDate) { statistics, _ in
                    let value = self.extractValue(from: statistics, for: type)
                    
                    print("Statistics: start=\(statistics.startDate), end=\(statistics.endDate), value=\(value)")
                    
                    let healthData = HealthData(
                        date: statistics.startDate,
                        value: value,
                        type: type
                    )
                    healthDataArray.append(healthData)
                }
                
                print("Total health data points collected: \(healthDataArray.count)")
                continuation.resume(returning: healthDataArray)
            }
            
            healthStore.execute(query)
        }
    }
    
    // MARK: - Helper Methods
    
    /// 创建日期组件
    private func createDateComponents(for component: Calendar.Component) -> DateComponents {
        var components = DateComponents()
        switch component {
        case .hour: components.hour = 1
        case .day: components.day = 1
        case .month: components.month = 1
        default: components.day = 1
        }
        return components
    }
    
    /// 获取统计选项
    private func getStatisticsOptions(for type: HealthDataType) -> HKStatisticsOptions {
        switch type {
        case .steps, .calories, .cyclingTime, .distance:
            return .cumulativeSum
        case .heartRate:
            return .discreteAverage
        }
    }
    
    /// 从统计数据中提取值
    private func extractValue(from statistics: HKStatistics, for type: HealthDataType) -> Double {
        switch type {
        case .steps, .calories, .cyclingTime, .distance:
            return statistics.sumQuantity()?.doubleValue(for: type.unit) ?? 0
        case .heartRate:
            return statistics.averageQuantity()?.doubleValue(for: type.unit) ?? 0
        }
    }
    
    /// 生成降级数据
    private func generateFallbackData(type: HealthDataType, period: TimePeriod) -> [HealthData] {
        let calendar = Calendar.current
        let startDate = period.currentPeriodStartDate
        let endDate = Date()
        
        var healthDataArray: [HealthData] = []
        var currentDate = startDate
        
        while currentDate < endDate {
            let value = generateMockValue(for: type, date: currentDate, period: period)
            
            let healthData = HealthData(
                date: currentDate,
                value: value,
                type: type
            )
            healthDataArray.append(healthData)
            
            currentDate = calendar.date(byAdding: period.aggregationInterval, value: 1, to: currentDate) ?? endDate
        }
        
        return healthDataArray
    }
    
    /// 生成模拟数据值
    private func generateMockValue(for type: HealthDataType, date: Date, period: TimePeriod) -> Double {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: date)
        let isWeekend = calendar.isDateInWeekend(date)
        
        switch type {
        case .steps:
            switch period.aggregationInterval {
            case .hour:
                return hour >= 6 && hour <= 22 ? Double.random(in: 100...800) : Double.random(in: 0...50)
            case .day:
                let baseSteps = isWeekend ? 6000 : 9000
                return Double(max(1000, baseSteps + Int.random(in: -2000...4000)))
            case .month:
                return Double.random(in: 150000...300000)
            default:
                return Double.random(in: 5000...12000)
            }
        case .calories:
            let steps = generateMockValue(for: .steps, date: date, period: period)
            return steps * 0.04
        case .heartRate:
            return Double.random(in: 60...100)
        case .cyclingTime:
            return Double.random(in: 0...60)
        case .distance:
            let steps = generateMockValue(for: .steps, date: date, period: period)
            return steps * 0.0008 // 大约每步0.8米
        }
    }
}
